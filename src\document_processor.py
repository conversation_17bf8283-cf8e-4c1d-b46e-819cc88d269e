"""
Document processing module for handling PDF and image uploads
"""
import os
import io
import base64
from typing import List, Dict, Any, Optional
from pathlib import Path
import hashlib

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("Warning: PyMuPDF not available. PDF processing will be limited.")

from PIL import Image
import requests
from loguru import logger

from config.settings import settings


class DocumentProcessor:
    """Handles document upload, validation, and basic processing"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Supported file types
        self.supported_pdf_types = {'.pdf'}
        self.supported_image_types = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        self.max_file_size = settings.MAX_FILE_SIZE_MB * 1024 * 1024  # Convert to bytes
        
    def validate_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Validate uploaded file"""
        file_ext = Path(filename).suffix.lower()
        file_size = len(file_content)
        
        validation_result = {
            'valid': True,
            'errors': [],
            'file_type': None,
            'file_size': file_size
        }
        
        # Check file size
        if file_size > self.max_file_size:
            validation_result['valid'] = False
            validation_result['errors'].append(
                f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({settings.MAX_FILE_SIZE_MB}MB)"
            )
        
        # Check file type
        if file_ext in self.supported_pdf_types:
            validation_result['file_type'] = 'pdf'
        elif file_ext in self.supported_image_types:
            validation_result['file_type'] = 'image'
        else:
            validation_result['valid'] = False
            validation_result['errors'].append(
                f"Unsupported file type: {file_ext}. Supported types: {self.supported_pdf_types | self.supported_image_types}"
            )
        
        return validation_result
    
    def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """Save uploaded file and return file path"""
        # Generate unique filename to avoid conflicts
        file_hash = hashlib.md5(file_content).hexdigest()[:8]
        file_ext = Path(filename).suffix.lower()
        unique_filename = f"{Path(filename).stem}_{file_hash}{file_ext}"
        
        file_path = self.upload_dir / unique_filename
        
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        logger.info(f"Saved uploaded file: {file_path}")
        return str(file_path)
    
    def extract_text_from_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Extract text from PDF using PyMuPDF"""
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF is required for PDF processing")
        
        pages_content = []
        
        try:
            doc = fitz.open(file_path)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                # Get page images if any
                image_list = page.get_images()
                images = []
                
                for img_index, img in enumerate(image_list):
                    try:
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)
                        
                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            img_data = pix.tobytes("png")
                            img_base64 = base64.b64encode(img_data).decode()
                            images.append({
                                'index': img_index,
                                'data': img_base64,
                                'format': 'png'
                            })
                        pix = None
                    except Exception as e:
                        logger.warning(f"Could not extract image {img_index} from page {page_num}: {e}")
                
                pages_content.append({
                    'page_number': page_num + 1,
                    'text': text.strip(),
                    'images': images,
                    'has_text': bool(text.strip()),
                    'has_images': bool(images)
                })
            
            doc.close()
            logger.info(f"Extracted content from {len(pages_content)} pages")
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            raise
        
        return pages_content
    
    def process_image(self, file_path: str) -> Dict[str, Any]:
        """Process image file"""
        try:
            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Get image info
                width, height = img.size
                
                # Convert to base64 for OCR processing
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG')
                img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
                
                return {
                    'width': width,
                    'height': height,
                    'format': 'png',
                    'data': img_base64,
                    'file_path': file_path
                }
                
        except Exception as e:
            logger.error(f"Error processing image {file_path}: {e}")
            raise
    
    def chunk_text(self, text: str, chunk_size: int = None, chunk_overlap: int = None) -> List[str]:
        """Split text into chunks for processing"""
        if chunk_size is None:
            chunk_size = settings.CHUNK_SIZE
        if chunk_overlap is None:
            chunk_overlap = settings.CHUNK_OVERLAP
        
        if not text.strip():
            return []
        
        # Simple sentence-aware chunking
        sentences = text.split('. ')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            # Add sentence to current chunk
            test_chunk = current_chunk + ". " + sentence if current_chunk else sentence
            
            if len(test_chunk) <= chunk_size:
                current_chunk = test_chunk
            else:
                # Current chunk is full, start new one
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # Handle overlap
                if chunk_overlap > 0 and current_chunk:
                    overlap_text = current_chunk[-chunk_overlap:]
                    current_chunk = overlap_text + ". " + sentence
                else:
                    current_chunk = sentence
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        logger.info(f"Split text into {len(chunks)} chunks")
        return chunks
    
    def process_document(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Main method to process uploaded document"""
        # Validate file
        validation = self.validate_file(file_content, filename)
        if not validation['valid']:
            return {
                'success': False,
                'errors': validation['errors']
            }
        
        try:
            # Save file
            file_path = self.save_uploaded_file(file_content, filename)
            
            result = {
                'success': True,
                'file_path': file_path,
                'file_type': validation['file_type'],
                'file_size': validation['file_size'],
                'filename': filename
            }
            
            if validation['file_type'] == 'pdf':
                # Extract content from PDF
                pages_content = self.extract_text_from_pdf(file_path)
                result['pages_content'] = pages_content
                result['total_pages'] = len(pages_content)
                
                # Combine all text for chunking
                all_text = "\n\n".join([page['text'] for page in pages_content if page['text']])
                result['text_chunks'] = self.chunk_text(all_text)
                
            elif validation['file_type'] == 'image':
                # Process image
                image_info = self.process_image(file_path)
                result['image_info'] = image_info
                result['text_chunks'] = []  # Will be filled after OCR
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {e}")
            return {
                'success': False,
                'errors': [f"Processing error: {str(e)}"]
            }
