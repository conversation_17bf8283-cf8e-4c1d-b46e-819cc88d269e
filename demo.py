"""
Demo script for StudyMate - Document RAG Chatbot
This script demonstrates the core functionality without the Streamlit UI
"""
import sys
from pathlib import Path
import time

# Add src to path
sys.path.append(str(Path(__file__).parent))

from config.settings import settings
from src.chat_engine import RAGChatEngine
from src.utils import setup_logging, validate_environment
from loguru import logger


def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("  StudyMate - Document RAG Chatbot Demo")
    print("=" * 60)
    print()


def print_section(title):
    """Print section header"""
    print(f"\n{'─' * 50}")
    print(f"  {title}")
    print(f"{'─' * 50}")


def demo_environment_validation():
    """Demo environment validation"""
    print_section("Environment Validation")
    
    validation_result = validate_environment()
    
    print(f"Environment Valid: {'✅' if validation_result['valid'] else '❌'}")
    
    if validation_result['errors']:
        print("\nErrors:")
        for error in validation_result['errors']:
            print(f"  ❌ {error}")
    
    if validation_result['warnings']:
        print("\nWarnings:")
        for warning in validation_result['warnings']:
            print(f"  ⚠️ {warning}")
    
    print("\nService Status:")
    services = validation_result.get('services', {})
    for service, status in services.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {service}")
    
    return validation_result['valid']


def demo_chat_engine():
    """Demo chat engine functionality"""
    print_section("Chat Engine Demo")
    
    try:
        # Initialize chat engine
        print("Initializing chat engine...")
        chat_engine = RAGChatEngine()
        print("✅ Chat engine initialized")
        
        # Get knowledge base stats
        stats = chat_engine.get_knowledge_base_stats()
        print(f"\nKnowledge Base Stats:")
        print(f"  Documents: {stats.get('total_documents', 0)}")
        print(f"  Dimension: {stats.get('dimension', 'N/A')}")
        print(f"  FAISS Available: {'✅' if stats.get('faiss_available', False) else '❌'}")
        
        return chat_engine
        
    except Exception as e:
        print(f"❌ Error initializing chat engine: {e}")
        return None


def demo_document_processing(chat_engine):
    """Demo document processing"""
    print_section("Document Processing Demo")
    
    if not chat_engine:
        print("❌ Chat engine not available")
        return
    
    # Create a sample text document
    sample_text = """
    This is a sample document for testing the StudyMate chatbot.
    
    The document contains information about artificial intelligence and machine learning.
    AI systems can process natural language and understand context.
    Machine learning algorithms learn from data to make predictions.
    
    This chatbot uses RAG (Retrieval-Augmented Generation) to answer questions
    based on the content of uploaded documents.
    """
    
    print("Creating sample document...")
    
    # Save sample as a text file and process it
    try:
        # For demo purposes, we'll simulate document processing
        print("✅ Sample document created")
        
        # Simulate adding document chunks
        chunks = [
            "This is a sample document for testing the StudyMate chatbot.",
            "The document contains information about artificial intelligence and machine learning.",
            "AI systems can process natural language and understand context.",
            "Machine learning algorithms learn from data to make predictions.",
            "This chatbot uses RAG (Retrieval-Augmented Generation) to answer questions based on the content of uploaded documents."
        ]
        
        metadata = [
            {'content': chunk, 'filename': 'sample.txt', 'chunk_index': i}
            for i, chunk in enumerate(chunks)
        ]
        
        success = chat_engine.vector_store.add_documents(chunks, metadata)
        
        if success:
            print("✅ Sample document processed and added to knowledge base")
            
            # Get updated stats
            stats = chat_engine.get_knowledge_base_stats()
            print(f"  Updated document count: {stats.get('total_documents', 0)}")
        else:
            print("❌ Failed to add sample document")
            
    except Exception as e:
        print(f"❌ Error processing sample document: {e}")


def demo_chat_interaction(chat_engine):
    """Demo chat interaction"""
    print_section("Chat Interaction Demo")
    
    if not chat_engine:
        print("❌ Chat engine not available")
        return
    
    # Sample questions
    questions = [
        "What is this document about?",
        "Tell me about artificial intelligence",
        "How does machine learning work?",
        "What is RAG?",
        "What technologies are mentioned?"
    ]
    
    print("Testing chat interactions...\n")
    
    for i, question in enumerate(questions, 1):
        print(f"Question {i}: {question}")
        
        try:
            # Get response
            response = chat_engine.chat(question, use_watson=False)
            
            if response['success']:
                print(f"Response: {response['response']}")
                print(f"Context Used: {'Yes' if response.get('context_used', False) else 'No'}")
                print(f"Retrieved Docs: {response.get('retrieved_docs_count', 0)}")
                print(f"Service: {response.get('service_used', 'Unknown')}")
            else:
                print(f"❌ Error: {response.get('error', 'Unknown error')}")
            
        except Exception as e:
            print(f"❌ Error processing question: {e}")
        
        print()
        time.sleep(1)  # Small delay between questions


def demo_cleanup(chat_engine):
    """Demo cleanup"""
    print_section("Cleanup")
    
    if chat_engine:
        try:
            chat_engine.clear_knowledge_base()
            print("✅ Knowledge base cleared")
        except Exception as e:
            print(f"⚠️ Error clearing knowledge base: {e}")


def main():
    """Main demo function"""
    print_banner()
    
    # Setup logging
    setup_logging()
    logger.info("Starting StudyMate demo")
    
    try:
        # Validate environment
        env_valid = demo_environment_validation()
        
        if not env_valid:
            print("\n❌ Environment validation failed. Please check your setup.")
            print("Run 'python setup.py' to fix common issues.")
            return
        
        # Initialize chat engine
        chat_engine = demo_chat_engine()
        
        # Demo document processing
        demo_document_processing(chat_engine)
        
        # Demo chat interaction
        demo_chat_interaction(chat_engine)
        
        # Cleanup
        demo_cleanup(chat_engine)
        
        print_section("Demo Complete")
        print("✅ Demo completed successfully!")
        print("\nTo run the full application:")
        print("  streamlit run app.py")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        logger.error(f"Demo error: {e}")
    
    print("\nThank you for trying StudyMate!")


if __name__ == "__main__":
    main()
