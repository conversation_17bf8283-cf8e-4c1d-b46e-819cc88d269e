2025-08-13 15:24:10.780 | INFO     | src.embeddings:_load_model:33 - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-08-13 15:26:22.821 | INFO     | src.embeddings:_load_model:35 - Embedding model loaded successfully
2025-08-13 15:26:22.822 | INFO     | src.embeddings:embed_texts:54 - Generating embeddings for 1 texts
2025-08-13 15:26:22.938 | INFO     | src.embeddings:embed_texts:56 - Generated embeddings with shape: (1, 384)
2025-08-13 15:26:22.939 | INFO     | src.vector_store:_load_index:66 - No existing index found, will create new one
2025-08-13 15:26:23.873 | ERROR    | src.chat_engine:_initialize_watson:56 - Failed to initialize IBM Watson: Error: Provided API key could not be found., Status code: 400
2025-08-13 15:26:23.891 | INFO     | src.chat_engine:__init__:167 - <PERSON>G Chat Engine initialized
2025-08-13 15:29:10.631 | WARNING  | src.vector_store:search:147 - FAISS index not available
2025-08-13 15:29:10.631 | WARNING  | src.vector_store:search:147 - FAISS index not available
2025-08-13 15:29:10.634 | ERROR    | src.chat_engine:chat:306 - Error processing chat message: 'Handler' object has no attribute 'formatter'
2025-08-13 15:29:10.634 | ERROR    | src.chat_engine:chat:306 - Error processing chat message: 'Handler' object has no attribute 'formatter'
