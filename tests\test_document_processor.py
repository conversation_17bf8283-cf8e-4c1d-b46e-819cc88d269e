"""
Tests for document processor module
"""
import pytest
import tempfile
import os
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.document_processor import DocumentProcessor
from src.utils import ValidationError


class TestDocumentProcessor:
    """Test cases for DocumentProcessor"""
    
    def setup_method(self):
        """Setup test environment"""
        self.processor = DocumentProcessor()
    
    def test_validate_file_valid_pdf(self):
        """Test validation of valid PDF file"""
        # Create dummy PDF content
        pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nxref\n0 1\n0000000000 65535 f \ntrailer\n<<\n/Size 1\n/Root 1 0 R\n>>\nstartxref\n9\n%%EOF"
        filename = "test.pdf"
        
        result = self.processor.validate_file(pdf_content, filename)
        
        assert result['valid'] == True
        assert result['file_type'] == 'pdf'
        assert len(result['errors']) == 0
    
    def test_validate_file_valid_image(self):
        """Test validation of valid image file"""
        # Create minimal PNG content
        png_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
        filename = "test.png"
        
        result = self.processor.validate_file(png_content, filename)
        
        assert result['valid'] == True
        assert result['file_type'] == 'image'
        assert len(result['errors']) == 0
    
    def test_validate_file_unsupported_type(self):
        """Test validation of unsupported file type"""
        content = b"some text content"
        filename = "test.txt"
        
        result = self.processor.validate_file(content, filename)
        
        assert result['valid'] == False
        assert len(result['errors']) > 0
        assert any("Unsupported file type" in error for error in result['errors'])
    
    def test_validate_file_too_large(self):
        """Test validation of file that's too large"""
        # Create content larger than max size
        large_content = b"x" * (self.processor.max_file_size + 1)
        filename = "large.pdf"
        
        result = self.processor.validate_file(large_content, filename)
        
        assert result['valid'] == False
        assert len(result['errors']) > 0
        assert any("exceeds maximum allowed size" in error for error in result['errors'])
    
    def test_chunk_text_basic(self):
        """Test basic text chunking"""
        text = "This is sentence one. This is sentence two. This is sentence three."
        
        chunks = self.processor.chunk_text(text, chunk_size=50, chunk_overlap=10)
        
        assert len(chunks) > 0
        assert all(isinstance(chunk, str) for chunk in chunks)
        assert all(len(chunk) <= 60 for chunk in chunks)  # Allow some overlap
    
    def test_chunk_text_empty(self):
        """Test chunking empty text"""
        text = ""
        
        chunks = self.processor.chunk_text(text)
        
        assert chunks == []
    
    def test_chunk_text_short(self):
        """Test chunking text shorter than chunk size"""
        text = "Short text."
        
        chunks = self.processor.chunk_text(text, chunk_size=100)
        
        assert len(chunks) == 1
        assert chunks[0] == text
    
    def test_save_uploaded_file(self):
        """Test saving uploaded file"""
        content = b"test content"
        filename = "test.txt"
        
        # Save file
        file_path = self.processor.save_uploaded_file(content, filename)
        
        # Check file exists
        assert os.path.exists(file_path)
        
        # Check content
        with open(file_path, 'rb') as f:
            saved_content = f.read()
        assert saved_content == content
        
        # Cleanup
        os.remove(file_path)
    
    def test_process_document_invalid_file(self):
        """Test processing invalid document"""
        content = b"invalid content"
        filename = "test.xyz"  # Unsupported extension
        
        result = self.processor.process_document(content, filename)
        
        assert result['success'] == False
        assert 'errors' in result
        assert len(result['errors']) > 0


class TestDocumentProcessorIntegration:
    """Integration tests for DocumentProcessor"""
    
    def setup_method(self):
        """Setup test environment"""
        self.processor = DocumentProcessor()
    
    def test_process_image_document(self):
        """Test processing image document (integration test)"""
        # Create a simple test image
        try:
            from PIL import Image
            import io
            
            # Create a simple test image
            img = Image.new('RGB', (100, 100), color='white')
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            img_content = img_buffer.getvalue()
            
            result = self.processor.process_document(img_content, "test.png")
            
            assert result['success'] == True
            assert result['file_type'] == 'image'
            assert 'image_info' in result
            
            # Cleanup
            if 'file_path' in result and os.path.exists(result['file_path']):
                os.remove(result['file_path'])
                
        except ImportError:
            pytest.skip("PIL not available for image processing test")
    
    def test_process_pdf_document_without_pymupdf(self):
        """Test processing PDF document when PyMuPDF is not available"""
        # Create minimal PDF content
        pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nxref\n0 1\n0000000000 65535 f \ntrailer\n<<\n/Size 1\n/Root 1 0 R\n>>\nstartxref\n9\n%%EOF"
        
        # This test will depend on whether PyMuPDF is available
        result = self.processor.process_document(pdf_content, "test.pdf")
        
        # Should either succeed or fail gracefully
        assert 'success' in result
        
        # Cleanup if file was created
        if result.get('success') and 'file_path' in result:
            if os.path.exists(result['file_path']):
                os.remove(result['file_path'])


if __name__ == "__main__":
    pytest.main([__file__])
