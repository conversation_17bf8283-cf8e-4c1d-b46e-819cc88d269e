"""
Simple run script for StudyMate - Document RAG Chatbot
"""
import sys
import subprocess
import os
from pathlib import Path


def check_requirements():
    """Check if basic requirements are met"""
    try:
        import streamlit
        return True
    except ImportError:
        return False


def main():
    """Main run function"""
    print("StudyMate - Document RAG Chatbot")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("app.py").exists():
        print("❌ Error: app.py not found")
        print("Please run this script from the StudyMate directory")
        return
    
    # Check requirements
    if not check_requirements():
        print("❌ Error: Required packages not installed")
        print("Please run: python setup.py")
        return
    
    # Check environment file
    if not Path(".env").exists():
        print("⚠️ Warning: .env file not found")
        print("Please copy .env.example to .env and configure your API keys")
        
        response = input("Continue anyway? (y/N): ").lower()
        if response != 'y':
            return
    
    print("🚀 Starting StudyMate...")
    print("The application will open in your default browser")
    print("Press Ctrl+C to stop the application")
    print()
    
    try:
        # Run Streamlit
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 StudyMate stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running application: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


if __name__ == "__main__":
    main()
