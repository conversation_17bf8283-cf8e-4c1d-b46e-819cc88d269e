"""
Chat engine integrating IBM Watson, Mistral AI, and RAG pipeline
"""
import json
from typing import List, Dict, Any, Optional
from loguru import logger

try:
    from ibm_watson import AssistantV2
    from ibm_cloud_sdk_core.authenticators import IAMAuthenticator
    IBM_WATSON_AVAILABLE = True
except ImportError:
    IBM_WATSON_AVAILABLE = False
    logger.warning("IBM Watson SDK not available")

import requests
from config.settings import settings
from src.vector_store import get_vector_store
from src.document_processor import DocumentProcessor
from src.ocr_service import get_ocr_service


class IBMWatsonService:
    """IBM Watson Assistant integration"""
    
    def __init__(self):
        self.api_key = settings.IBM_WATSON_API_KEY
        self.url = settings.IBM_WATSON_URL
        self.version = settings.IBM_WATSON_VERSION
        self.assistant_id = settings.IBM_WATSON_ASSISTANT_ID
        self.assistant = None
        self.session_id = None
        
        if IBM_WATSON_AVAILABLE and self.api_key:
            self._initialize_watson()
    
    def _initialize_watson(self):
        """Initialize Watson Assistant"""
        try:
            authenticator = IAMAuthenticator(self.api_key)
            self.assistant = AssistantV2(
                version=self.version,
                authenticator=authenticator
            )
            self.assistant.set_service_url(self.url)
            
            # Create session
            response = self.assistant.create_session(
                assistant_id=self.assistant_id
            ).get_result()
            self.session_id = response['session_id']
            
            logger.info("IBM Watson Assistant initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize IBM Watson: {e}")
            self.assistant = None
    
    def send_message(self, message: str, context: str = "") -> Dict[str, Any]:
        """Send message to Watson Assistant"""
        if not self.assistant or not self.session_id:
            return {
                'success': False,
                'response': 'Watson Assistant not available',
                'error': 'Service not initialized'
            }
        
        try:
            # Prepare message with context
            full_message = f"Context: {context}\n\nQuestion: {message}" if context else message
            
            response = self.assistant.message(
                assistant_id=self.assistant_id,
                session_id=self.session_id,
                input={
                    'message_type': 'text',
                    'text': full_message
                }
            ).get_result()
            
            # Extract response text
            response_text = ""
            if 'output' in response and 'generic' in response['output']:
                for item in response['output']['generic']:
                    if item.get('response_type') == 'text':
                        response_text += item.get('text', '')
            
            return {
                'success': True,
                'response': response_text,
                'full_response': response
            }
            
        except Exception as e:
            logger.error(f"Error sending message to Watson: {e}")
            return {
                'success': False,
                'response': 'Error communicating with Watson Assistant',
                'error': str(e)
            }


class MistralChatService:
    """Mistral AI chat service"""
    
    def __init__(self):
        self.api_key = settings.MISTRAL_API_KEY
        self.model = settings.MISTRAL_MODEL
        self.max_tokens = settings.MISTRAL_MAX_TOKENS
        self.temperature = settings.MISTRAL_TEMPERATURE
    
    def generate_response(self, prompt: str, context: str = "") -> Dict[str, Any]:
        """Generate response using Mistral AI"""
        if not self.api_key:
            return {
                'success': False,
                'response': 'Mistral API key not configured',
                'error': 'API key missing'
            }
        
        try:
            # Prepare the full prompt
            system_prompt = """You are a helpful AI assistant that answers questions based on provided context. 
            If the context contains relevant information, use it to answer the question. 
            If the context doesn't contain relevant information, say so clearly.
            Be concise and accurate in your responses."""
            
            full_prompt = f"Context:\n{context}\n\nQuestion: {prompt}\n\nAnswer:" if context else prompt
            
            # For now, return a placeholder response
            # In a real implementation, you would call Mistral's API
            response_text = self._call_mistral_api(system_prompt, full_prompt)
            
            return {
                'success': True,
                'response': response_text,
                'model': self.model
            }
            
        except Exception as e:
            logger.error(f"Error generating Mistral response: {e}")
            return {
                'success': False,
                'response': 'Error generating response',
                'error': str(e)
            }
    
    def _call_mistral_api(self, system_prompt: str, user_prompt: str) -> str:
        """Call Mistral API (placeholder implementation)"""
        # Placeholder for actual Mistral API call
        # Replace with real API integration
        return f"This is a placeholder response from Mistral AI. In a real implementation, this would call the Mistral API with the prompt: {user_prompt[:100]}..."


class RAGChatEngine:
    """Main RAG chat engine combining all services"""
    
    def __init__(self):
        self.vector_store = get_vector_store()
        self.document_processor = DocumentProcessor()
        self.ocr_service = get_ocr_service()
        self.watson_service = IBMWatsonService() if IBM_WATSON_AVAILABLE else None
        self.mistral_service = MistralChatService()
        
        self.chat_history = []
        
        logger.info("RAG Chat Engine initialized")
    
    def add_document(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Add a document to the knowledge base"""
        try:
            # Process document
            result = self.document_processor.process_document(file_content, filename)
            
            if not result['success']:
                return result
            
            # Extract text chunks
            chunks = []
            metadata = []
            
            if result['file_type'] == 'pdf':
                # Process PDF with OCR if needed
                pages_content = result['pages_content']
                
                # Apply OCR to images in PDF
                if any(page.get('images') for page in pages_content):
                    pages_content = self.ocr_service.process_pdf_images(pages_content)
                
                # Extract chunks from all pages
                for page in pages_content:
                    page_text = page.get('combined_text', page.get('text', ''))
                    if page_text.strip():
                        page_chunks = self.document_processor.chunk_text(page_text)
                        chunks.extend(page_chunks)
                        
                        # Add metadata for each chunk
                        for i, chunk in enumerate(page_chunks):
                            metadata.append({
                                'content': chunk,
                                'filename': filename,
                                'file_type': 'pdf',
                                'page_number': page['page_number'],
                                'chunk_index': i,
                                'has_ocr': page.get('ocr_texts', []) != []
                            })
            
            elif result['file_type'] == 'image':
                # Process image with OCR
                image_info = result['image_info']
                ocr_result = self.ocr_service.extract_text_from_image(
                    image_info['data'], 
                    image_info['format']
                )
                
                if ocr_result['success'] and ocr_result['extracted_text'].strip():
                    text_chunks = self.document_processor.chunk_text(ocr_result['extracted_text'])
                    chunks.extend(text_chunks)
                    
                    for i, chunk in enumerate(text_chunks):
                        metadata.append({
                            'content': chunk,
                            'filename': filename,
                            'file_type': 'image',
                            'chunk_index': i,
                            'ocr_confidence': ocr_result['confidence']
                        })
            
            # Add to vector store
            if chunks:
                success = self.vector_store.add_documents(chunks, metadata)
                if success:
                    logger.info(f"Added {len(chunks)} chunks from {filename} to knowledge base")
                    return {
                        'success': True,
                        'message': f"Successfully processed {filename}",
                        'chunks_added': len(chunks),
                        'file_type': result['file_type']
                    }
                else:
                    return {
                        'success': False,
                        'errors': ['Failed to add document to vector store']
                    }
            else:
                return {
                    'success': False,
                    'errors': ['No text content found in document']
                }
                
        except Exception as e:
            logger.error(f"Error adding document {filename}: {e}")
            return {
                'success': False,
                'errors': [f"Processing error: {str(e)}"]
            }
    
    def chat(self, message: str, use_watson: bool = False) -> Dict[str, Any]:
        """Process chat message with RAG"""
        try:
            # Retrieve relevant documents
            retrieved_docs = self.vector_store.search(message, top_k=settings.MAX_RETRIEVAL_DOCS)
            
            # Prepare context from retrieved documents
            context = ""
            if retrieved_docs:
                context_parts = []
                for i, doc in enumerate(retrieved_docs[:5]):  # Use top 5 results
                    context_parts.append(f"[Document {i+1}] {doc['content']}")
                context = "\n\n".join(context_parts)
            
            # Generate response
            if use_watson and self.watson_service:
                response_result = self.watson_service.send_message(message, context)
            else:
                response_result = self.mistral_service.generate_response(message, context)
            
            # Prepare chat result
            chat_result = {
                'success': response_result['success'],
                'message': message,
                'response': response_result['response'],
                'context_used': bool(context),
                'retrieved_docs_count': len(retrieved_docs),
                'service_used': 'watson' if use_watson else 'mistral'
            }
            
            if not response_result['success']:
                chat_result['error'] = response_result.get('error', 'Unknown error')
            
            # Add to chat history
            self.chat_history.append({
                'message': message,
                'response': response_result['response'],
                'timestamp': logger._core.handlers[0].formatter._fmt if hasattr(logger, '_core') else 'now',
                'context_used': bool(context)
            })
            
            # Keep only last 50 messages
            if len(self.chat_history) > 50:
                self.chat_history = self.chat_history[-50:]
            
            return chat_result
            
        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            return {
                'success': False,
                'message': message,
                'response': 'Sorry, I encountered an error processing your message.',
                'error': str(e)
            }
    
    def get_knowledge_base_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base"""
        return self.vector_store.get_stats()
    
    def clear_knowledge_base(self):
        """Clear the knowledge base"""
        self.vector_store.clear()
        logger.info("Knowledge base cleared")
    
    def get_chat_history(self) -> List[Dict[str, Any]]:
        """Get chat history"""
        return self.chat_history.copy()
    
    def clear_chat_history(self):
        """Clear chat history"""
        self.chat_history = []
        logger.info("Chat history cleared")
