# StudyMate - Document-Based RAG Chatbot

A comprehensive Retrieval-Augmented Generation (RAG) chatbot system that processes PDF documents and images, extracts content using OCR, and provides intelligent responses based on document content.

## Features

- **Multi-format Document Support**: Process PDF documents and images
- **Advanced OCR**: Mistral OCR via Azure AI for text extraction
- **Intelligent Retrieval**: FAISS vector database with Cohere embeddings
- **Smart Reranking**: Cohere reranker for improved relevance
- **Powerful LLM**: GPT-4.1 via Azure OpenAI for response generation
- **User-friendly Interface**: Web-based chat interface

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Document      │    │   OCR & Text     │    │   Chunking &    │
│   Upload        │───▶│   Extraction     │───▶│   Processing    │
│   (PDF/Images)  │    │   (Mistral OCR)  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Response      │    │   LLM Generation │    │   Vector Store  │
│   Display       │◀───│   (GPT-4.1)      │◀───│   (FAISS +     │
│                 │    │                  │    │   Cohere)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Retrieval &    │◀───│   User Query    │
                       │   Reranking      │    │   Processing    │
                       │   (Cohere)       │    │                 │
                       └──────────────────┘    └─────────────────┘
```

## Technology Stack

- **Web Framework**: Streamlit for user interface
- **LLM Services**: IBM Watson Assistant & Mistral AI
- **Vector Database**: FAISS for document retrieval
- **Embeddings**: HuggingFace sentence-transformers
- **Document Processing**: PyMuPDF for PDF handling
- **OCR**: Mistral AI for text extraction from images
- **Language**: Python 3.8+

## Quick Start

### Option 1: Automated Setup
```bash
python setup.py
```

### Option 2: Manual Setup

1. **Install Dependencies**:
   ```bash
   pip install streamlit PyMuPDF faiss-cpu sentence-transformers python-dotenv loguru pandas numpy requests Pillow ibm-watson
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys (see API Configuration section)
   ```

3. **Run the Application**:
   ```bash
   streamlit run app.py
   ```

4. **Access the Application**:
   - Open your browser to `http://localhost:8501`
   - Upload documents using the sidebar
   - Start chatting about your documents!

## Project Structure

```
StudyMate/
├── app.py                 # Main Streamlit application
├── config/
│   ├── __init__.py
│   └── settings.py        # Configuration management
├── src/
│   ├── __init__.py
│   ├── document_processor.py  # PDF/Image processing
│   ├── ocr_service.py         # Mistral OCR integration
│   ├── vector_store.py        # FAISS vector database
│   ├── embeddings.py          # Cohere embeddings
│   ├── retriever.py           # Retrieval and reranking
│   └── chat_engine.py         # RAG pipeline
├── tests/
│   ├── __init__.py
│   ├── test_document_processor.py
│   ├── test_ocr_service.py
│   └── test_rag_pipeline.py
├── data/
│   ├── uploads/           # Uploaded documents
│   └── vector_store/      # FAISS indices
├── requirements.txt
├── .env.example
├── .env
└── README.md
```

## API Configuration

Configure the following services in your `.env` file:

### Required Services

1. **IBM Watson Assistant**:
   ```env
   IBM_WATSON_API_KEY=your_api_key_here
   IBM_WATSON_URL=https://api.us-south.assistant.watson.cloud.ibm.com
   IBM_WATSON_ASSISTANT_ID=your_assistant_id_here
   ```

2. **Mistral AI** (Alternative to Watson):
   ```env
   MISTRAL_API_KEY=your_mistral_api_key_here
   MISTRAL_MODEL=mistral-large-latest
   ```

### Optional Services

3. **HuggingFace** (for better embeddings):
   ```env
   HUGGINGFACE_API_KEY=your_huggingface_api_key_here
   ```

### Service Setup Instructions

1. **IBM Watson**:
   - Create an account at [IBM Cloud](https://cloud.ibm.com)
   - Create a Watson Assistant service
   - Get your API key and Assistant ID

2. **Mistral AI**:
   - Sign up at [Mistral AI](https://mistral.ai)
   - Get your API key from the dashboard

3. **HuggingFace** (Optional):
   - Create account at [HuggingFace](https://huggingface.co)
   - Generate an API token in settings

## Features in Detail

### Document Processing
- **PDF Support**: Extract text and images from PDF documents
- **Image Support**: Process PNG, JPG, JPEG, BMP, TIFF, WebP images
- **OCR Integration**: Extract text from images using Mistral AI
- **Smart Chunking**: Intelligent text segmentation for optimal retrieval

### RAG Pipeline
- **Vector Search**: FAISS-based similarity search
- **Embeddings**: HuggingFace sentence-transformers for semantic understanding
- **Context Retrieval**: Relevant document chunks for accurate responses
- **Fallback Systems**: Graceful degradation when services are unavailable

### Chat Interface
- **Dual LLM Support**: Choose between IBM Watson and Mistral AI
- **Real-time Chat**: Interactive conversation with document context
- **Response Metadata**: See which documents were used for responses
- **Chat History**: Persistent conversation history

## Development

### Running Tests
```bash
pytest tests/ -v
```

### Code Quality
```bash
# Format code
black src/ tests/ app.py

# Lint code
flake8 src/ tests/ app.py
```

### Project Structure Details
```
StudyMate/
├── app.py                     # Main Streamlit application
├── setup.py                   # Automated setup script
├── requirements.txt           # Python dependencies
├── .env                       # Environment variables (create from .env.example)
├── config/
│   └── settings.py           # Configuration management
├── src/
│   ├── document_processor.py # PDF/Image processing
│   ├── ocr_service.py        # Mistral OCR integration
│   ├── embeddings.py         # HuggingFace embeddings
│   ├── vector_store.py       # FAISS vector database
│   ├── chat_engine.py        # Main RAG pipeline
│   └── utils.py              # Utilities and error handling
├── tests/                    # Unit tests
├── data/
│   ├── uploads/              # Uploaded documents
│   └── vector_store/         # FAISS indices
└── logs/                     # Application logs
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Run `python setup.py` to install dependencies
2. **API Errors**: Check your API keys in `.env` file
3. **File Upload Issues**: Ensure file size is under 50MB
4. **Memory Issues**: Use smaller documents or increase system memory

### Fallback Modes

The system includes fallback modes for robustness:
- **No FAISS**: Uses simple text-based search
- **No HuggingFace**: Uses basic text feature embeddings
- **No OCR**: Processes only text-based content
- **No LLM APIs**: Shows retrieved context without generation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details
