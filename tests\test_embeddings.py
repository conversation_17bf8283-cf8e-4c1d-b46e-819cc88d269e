"""
Tests for embeddings module
"""
import pytest
import numpy as np
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.embeddings import (
    HuggingFaceEmbeddingService, 
    FallbackEmbeddingService, 
    DocumentEmbedder,
    get_embedding_service
)


class TestFallbackEmbeddingService:
    """Test cases for FallbackEmbeddingService"""
    
    def setup_method(self):
        """Setup test environment"""
        self.service = FallbackEmbeddingService()
    
    def test_embed_single_text(self):
        """Test embedding single text"""
        text = "This is a test sentence."
        
        embedding = self.service.embed_single_text(text)
        
        assert isinstance(embedding, np.ndarray)
        assert len(embedding) == self.service.dimension
        assert not np.allclose(embedding, 0)  # Should not be all zeros
    
    def test_embed_texts(self):
        """Test embedding multiple texts"""
        texts = [
            "First test sentence.",
            "Second test sentence.",
            "Third test sentence."
        ]
        
        embeddings = self.service.embed_texts(texts)
        
        assert isinstance(embeddings, np.ndarray)
        assert embeddings.shape == (len(texts), self.service.dimension)
        
        # Each embedding should be different
        for i in range(len(texts)):
            for j in range(i + 1, len(texts)):
                assert not np.allclose(embeddings[i], embeddings[j])
    
    def test_embed_empty_text(self):
        """Test embedding empty text"""
        text = ""
        
        embedding = self.service.embed_single_text(text)
        
        assert isinstance(embedding, np.ndarray)
        assert len(embedding) == self.service.dimension
        assert np.allclose(embedding, 0)  # Should be all zeros for empty text
    
    def test_embed_empty_list(self):
        """Test embedding empty list"""
        texts = []
        
        embeddings = self.service.embed_texts(texts)
        
        assert isinstance(embeddings, np.ndarray)
        assert embeddings.shape == (0, self.service.dimension)
    
    def test_get_embedding_dimension(self):
        """Test getting embedding dimension"""
        dimension = self.service.get_embedding_dimension()
        
        assert isinstance(dimension, int)
        assert dimension > 0
        assert dimension == self.service.dimension


class TestDocumentEmbedder:
    """Test cases for DocumentEmbedder"""
    
    def setup_method(self):
        """Setup test environment"""
        self.embedder = DocumentEmbedder()
    
    def test_embed_document_chunks(self):
        """Test embedding document chunks"""
        chunks = [
            "This is the first chunk of text.",
            "This is the second chunk of text.",
            "This is the third chunk of text."
        ]
        
        result = self.embedder.embed_document_chunks(chunks)
        
        assert 'embeddings' in result
        assert 'chunks' in result
        assert 'metadata' in result
        assert 'dimension' in result
        
        assert isinstance(result['embeddings'], np.ndarray)
        assert result['embeddings'].shape[0] == len(chunks)
        assert result['embeddings'].shape[1] == self.embedder.dimension
        
        assert result['chunks'] == chunks
        assert len(result['metadata']) == len(chunks)
        assert result['dimension'] == self.embedder.dimension
    
    def test_embed_document_chunks_with_metadata(self):
        """Test embedding document chunks with custom metadata"""
        chunks = [
            "First chunk",
            "Second chunk"
        ]
        metadata = [
            {'source': 'doc1', 'page': 1},
            {'source': 'doc1', 'page': 2}
        ]
        
        result = self.embedder.embed_document_chunks(chunks, metadata)
        
        assert result['metadata'] == metadata
    
    def test_embed_document_chunks_empty(self):
        """Test embedding empty chunks"""
        chunks = []
        
        result = self.embedder.embed_document_chunks(chunks)
        
        assert result['embeddings'].shape == (0,)
        assert result['chunks'] == []
        assert result['metadata'] == []
    
    def test_embed_query(self):
        """Test embedding query"""
        query = "What is the main topic of the document?"
        
        embedding = self.embedder.embed_query(query)
        
        assert isinstance(embedding, np.ndarray)
        assert len(embedding) == self.embedder.dimension
        assert not np.allclose(embedding, 0)


class TestEmbeddingServiceFactory:
    """Test cases for embedding service factory"""
    
    def test_get_embedding_service(self):
        """Test getting embedding service"""
        service = get_embedding_service()
        
        # Should return either HuggingFace or Fallback service
        assert isinstance(service, (HuggingFaceEmbeddingService, FallbackEmbeddingService))
        
        # Should have required methods
        assert hasattr(service, 'embed_texts')
        assert hasattr(service, 'embed_single_text')
        assert hasattr(service, 'get_embedding_dimension')


class TestHuggingFaceEmbeddingService:
    """Test cases for HuggingFaceEmbeddingService (if available)"""
    
    def setup_method(self):
        """Setup test environment"""
        try:
            from sentence_transformers import SentenceTransformer
            self.service = HuggingFaceEmbeddingService()
            self.available = True
        except ImportError:
            self.available = False
    
    def test_embed_single_text_if_available(self):
        """Test embedding single text with HuggingFace (if available)"""
        if not self.available:
            pytest.skip("sentence-transformers not available")
        
        if self.service.model is None:
            pytest.skip("HuggingFace model not loaded")
        
        text = "This is a test sentence."
        
        embedding = self.service.embed_single_text(text)
        
        assert isinstance(embedding, np.ndarray)
        assert len(embedding) > 0
        assert not np.allclose(embedding, 0)
    
    def test_embed_texts_if_available(self):
        """Test embedding multiple texts with HuggingFace (if available)"""
        if not self.available:
            pytest.skip("sentence-transformers not available")
        
        if self.service.model is None:
            pytest.skip("HuggingFace model not loaded")
        
        texts = [
            "First test sentence.",
            "Second test sentence."
        ]
        
        embeddings = self.service.embed_texts(texts)
        
        assert isinstance(embeddings, np.ndarray)
        assert embeddings.shape[0] == len(texts)
        assert embeddings.shape[1] > 0


if __name__ == "__main__":
    pytest.main([__file__])
