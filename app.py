"""
StudyMate - Document-Based RAG Chatbot
Main Streamlit Application
"""
import streamlit as st
import os
from pathlib import Path
import time
from typing import Dict, Any

# Add src to path
import sys
sys.path.append(str(Path(__file__).parent))

from config.settings import settings
from src.chat_engine import RAGChatEngine
from loguru import logger

# Configure logging
logger.add("logs/app.log", rotation="1 day", retention="7 days", level=settings.LOG_LEVEL)

# Page configuration
st.set_page_config(
    page_title=settings.APP_TITLE,
    page_icon="📚",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .bot-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .stats-box {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'chat_engine' not in st.session_state:
    st.session_state.chat_engine = RAGChatEngine()
    st.session_state.messages = []
    st.session_state.documents_uploaded = 0

def main():
    """Main application function"""
    
    # Header
    st.markdown('<h1 class="main-header">📚 StudyMate - Document RAG Chatbot</h1>', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.header("📁 Document Management")
        
        # File upload
        uploaded_files = st.file_uploader(
            "Upload Documents",
            type=['pdf', 'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'webp'],
            accept_multiple_files=True,
            help="Upload PDF documents or images to add to the knowledge base"
        )
        
        # Process uploaded files
        if uploaded_files:
            process_uploaded_files(uploaded_files)
        
        st.divider()
        
        # Knowledge base stats
        st.header("📊 Knowledge Base Stats")
        display_knowledge_base_stats()
        
        st.divider()
        
        # Settings
        st.header("⚙️ Settings")
        
        # Service selection
        use_watson = st.checkbox(
            "Use IBM Watson",
            value=False,
            help="Use IBM Watson Assistant instead of Mistral AI"
        )
        
        # Clear options
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Clear Chat", type="secondary"):
                st.session_state.messages = []
                st.session_state.chat_engine.clear_chat_history()
                st.rerun()
        
        with col2:
            if st.button("Clear KB", type="secondary", help="Clear Knowledge Base"):
                st.session_state.chat_engine.clear_knowledge_base()
                st.session_state.documents_uploaded = 0
                st.rerun()
    
    # Main chat interface
    st.header("💬 Chat Interface")
    
    # Display chat messages
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.messages:
            display_message(message)
    
    # Chat input
    if prompt := st.chat_input("Ask a question about your documents..."):
        # Add user message
        user_message = {"role": "user", "content": prompt}
        st.session_state.messages.append(user_message)
        
        # Display user message
        with chat_container:
            display_message(user_message)
        
        # Generate response
        with st.spinner("Thinking..."):
            response_result = st.session_state.chat_engine.chat(prompt, use_watson=use_watson)
        
        # Add assistant response
        assistant_message = {
            "role": "assistant", 
            "content": response_result['response'],
            "metadata": {
                "success": response_result['success'],
                "context_used": response_result.get('context_used', False),
                "retrieved_docs_count": response_result.get('retrieved_docs_count', 0),
                "service_used": response_result.get('service_used', 'unknown')
            }
        }
        st.session_state.messages.append(assistant_message)
        
        # Display assistant response
        with chat_container:
            display_message(assistant_message)
        
        # Show error if any
        if not response_result['success']:
            st.error(f"Error: {response_result.get('error', 'Unknown error')}")

def process_uploaded_files(uploaded_files):
    """Process uploaded files"""
    for uploaded_file in uploaded_files:
        if uploaded_file is not None:
            with st.spinner(f"Processing {uploaded_file.name}..."):
                try:
                    # Read file content
                    file_content = uploaded_file.read()
                    
                    # Process document
                    result = st.session_state.chat_engine.add_document(
                        file_content, 
                        uploaded_file.name
                    )
                    
                    if result['success']:
                        st.success(f"✅ Successfully processed {uploaded_file.name}")
                        st.session_state.documents_uploaded += 1
                        
                        # Show processing details
                        with st.expander(f"Details for {uploaded_file.name}"):
                            st.write(f"**File Type:** {result.get('file_type', 'Unknown')}")
                            st.write(f"**Chunks Added:** {result.get('chunks_added', 0)}")
                    else:
                        st.error(f"❌ Failed to process {uploaded_file.name}")
                        if 'errors' in result:
                            for error in result['errors']:
                                st.error(f"Error: {error}")
                
                except Exception as e:
                    st.error(f"❌ Error processing {uploaded_file.name}: {str(e)}")
                    logger.error(f"Error processing uploaded file {uploaded_file.name}: {e}")

def display_knowledge_base_stats():
    """Display knowledge base statistics"""
    try:
        stats = st.session_state.chat_engine.get_knowledge_base_stats()
        
        st.markdown('<div class="stats-box">', unsafe_allow_html=True)
        st.metric("Documents", st.session_state.documents_uploaded)
        st.metric("Text Chunks", stats.get('total_documents', 0))
        st.metric("Vector Dimension", stats.get('dimension', 'N/A'))
        
        # Service status
        st.write("**Services:**")
        st.write(f"• FAISS: {'✅' if stats.get('faiss_available', False) else '❌'}")
        st.write(f"• Vector Store: {'✅' if stats.get('index_available', False) else '❌'}")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
    except Exception as e:
        st.error(f"Error loading stats: {e}")

def display_message(message: Dict[str, Any]):
    """Display a chat message"""
    role = message["role"]
    content = message["content"]
    
    if role == "user":
        st.markdown(
            f'<div class="chat-message user-message"><strong>You:</strong><br>{content}</div>',
            unsafe_allow_html=True
        )
    else:
        # Assistant message
        metadata = message.get("metadata", {})
        
        st.markdown(
            f'<div class="chat-message bot-message"><strong>StudyMate:</strong><br>{content}</div>',
            unsafe_allow_html=True
        )
        
        # Show metadata if available
        if metadata:
            with st.expander("Response Details", expanded=False):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Service:** {metadata.get('service_used', 'Unknown')}")
                
                with col2:
                    st.write(f"**Context Used:** {'Yes' if metadata.get('context_used', False) else 'No'}")
                
                with col3:
                    st.write(f"**Docs Retrieved:** {metadata.get('retrieved_docs_count', 0)}")

if __name__ == "__main__":
    try:
        # Validate settings
        settings.validate_required_settings()
        main()
    except ValueError as e:
        st.error(f"Configuration Error: {e}")
        st.info("Please check your environment variables in the .env file")
    except Exception as e:
        st.error(f"Application Error: {e}")
        logger.error(f"Application error: {e}")
        st.info("Please check the logs for more details")
