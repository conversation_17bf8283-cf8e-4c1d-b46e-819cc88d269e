"""
Vector store implementation using FAISS for document retrieval
"""
import os
import pickle
import json
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import numpy as np
from loguru import logger

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logger.warning("FAISS not available. Using fallback vector store.")

from config.settings import settings
from src.embeddings import DocumentEmbedder


class FAISSVectorStore:
    """FAISS-based vector store for document retrieval"""
    
    def __init__(self, index_name: str = "default"):
        self.index_name = index_name
        self.vector_store_dir = Path(settings.VECTOR_STORE_DIR)
        self.vector_store_dir.mkdir(parents=True, exist_ok=True)
        
        self.index_path = self.vector_store_dir / f"{index_name}.faiss"
        self.metadata_path = self.vector_store_dir / f"{index_name}_metadata.pkl"
        self.config_path = self.vector_store_dir / f"{index_name}_config.json"
        
        self.index = None
        self.metadata = []
        self.dimension = None
        self.embedder = DocumentEmbedder()
        
        # Load existing index if available
        self._load_index()
    
    def _load_index(self):
        """Load existing FAISS index and metadata"""
        if not FAISS_AVAILABLE:
            logger.warning("FAISS not available, using fallback")
            return
        
        try:
            if self.index_path.exists() and self.metadata_path.exists():
                # Load FAISS index
                self.index = faiss.read_index(str(self.index_path))
                
                # Load metadata
                with open(self.metadata_path, 'rb') as f:
                    self.metadata = pickle.load(f)
                
                # Load config
                if self.config_path.exists():
                    with open(self.config_path, 'r') as f:
                        config = json.load(f)
                        self.dimension = config.get('dimension')
                
                logger.info(f"Loaded existing index with {len(self.metadata)} documents")
            else:
                logger.info("No existing index found, will create new one")
                
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            self.index = None
            self.metadata = []
    
    def _save_index(self):
        """Save FAISS index and metadata"""
        if not FAISS_AVAILABLE or self.index is None:
            return
        
        try:
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_path))
            
            # Save metadata
            with open(self.metadata_path, 'wb') as f:
                pickle.dump(self.metadata, f)
            
            # Save config
            config = {
                'dimension': self.dimension,
                'index_name': self.index_name,
                'total_documents': len(self.metadata)
            }
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Saved index with {len(self.metadata)} documents")
            
        except Exception as e:
            logger.error(f"Error saving index: {e}")
    
    def add_documents(self, chunks: List[str], metadata: List[Dict[str, Any]]) -> bool:
        """Add documents to the vector store"""
        if not FAISS_AVAILABLE:
            logger.warning("FAISS not available, cannot add documents")
            return False
        
        if not chunks:
            logger.warning("No chunks to add")
            return False
        
        try:
            # Generate embeddings
            embedding_result = self.embedder.embed_document_chunks(chunks, metadata)
            embeddings = embedding_result['embeddings']
            
            if len(embeddings) == 0:
                logger.warning("No embeddings generated")
                return False
            
            # Initialize index if needed
            if self.index is None:
                self.dimension = embedding_result['dimension']
                self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
                logger.info(f"Created new FAISS index with dimension {self.dimension}")
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embeddings)
            
            # Add to index
            self.index.add(embeddings.astype(np.float32))
            
            # Add metadata
            self.metadata.extend(embedding_result['metadata'])
            
            # Save index
            self._save_index()
            
            logger.info(f"Added {len(chunks)} documents to vector store")
            return True
            
        except Exception as e:
            logger.error(f"Error adding documents to vector store: {e}")
            return False
    
    def search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """Search for similar documents"""
        if not FAISS_AVAILABLE or self.index is None:
            logger.warning("FAISS index not available")
            return []
        
        try:
            # Generate query embedding
            query_embedding = self.embedder.embed_query(query)
            
            if len(query_embedding) == 0:
                logger.warning("Empty query embedding")
                return []
            
            # Normalize query embedding
            query_embedding = query_embedding.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_embedding)
            
            # Search
            scores, indices = self.index.search(query_embedding, min(top_k, len(self.metadata)))
            
            # Prepare results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx >= 0 and idx < len(self.metadata):
                    result = {
                        'content': self.metadata[idx].get('content', ''),
                        'score': float(score),
                        'metadata': self.metadata[idx],
                        'rank': i + 1
                    }
                    results.append(result)
            
            logger.info(f"Found {len(results)} results for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics"""
        stats = {
            'total_documents': len(self.metadata),
            'dimension': self.dimension,
            'index_available': self.index is not None,
            'faiss_available': FAISS_AVAILABLE
        }
        
        if FAISS_AVAILABLE and self.index is not None:
            stats['index_size'] = self.index.ntotal
        
        return stats
    
    def clear(self):
        """Clear the vector store"""
        try:
            self.index = None
            self.metadata = []
            self.dimension = None
            
            # Remove files
            for path in [self.index_path, self.metadata_path, self.config_path]:
                if path.exists():
                    path.unlink()
            
            logger.info("Cleared vector store")
            
        except Exception as e:
            logger.error(f"Error clearing vector store: {e}")


class FallbackVectorStore:
    """Fallback vector store using simple similarity"""
    
    def __init__(self, index_name: str = "default"):
        self.index_name = index_name
        self.documents = []
        self.embedder = DocumentEmbedder()
        logger.warning("Using fallback vector store")
    
    def add_documents(self, chunks: List[str], metadata: List[Dict[str, Any]]) -> bool:
        """Add documents to fallback store"""
        try:
            embedding_result = self.embedder.embed_document_chunks(chunks, metadata)
            
            for i, chunk in enumerate(chunks):
                doc = {
                    'content': chunk,
                    'embedding': embedding_result['embeddings'][i] if len(embedding_result['embeddings']) > i else None,
                    'metadata': embedding_result['metadata'][i] if len(embedding_result['metadata']) > i else {}
                }
                self.documents.append(doc)
            
            logger.info(f"Added {len(chunks)} documents to fallback store")
            return True
            
        except Exception as e:
            logger.error(f"Error adding documents to fallback store: {e}")
            return False
    
    def search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """Simple text-based search"""
        if not self.documents:
            return []
        
        try:
            query_lower = query.lower()
            results = []
            
            for i, doc in enumerate(self.documents):
                content = doc['content'].lower()
                
                # Simple scoring based on keyword matches
                score = 0.0
                query_words = query_lower.split()
                
                for word in query_words:
                    if word in content:
                        score += 1.0 / len(query_words)
                
                if score > 0:
                    results.append({
                        'content': doc['content'],
                        'score': score,
                        'metadata': doc['metadata'],
                        'rank': 0  # Will be set after sorting
                    })
            
            # Sort by score
            results.sort(key=lambda x: x['score'], reverse=True)
            
            # Set ranks and limit results
            for i, result in enumerate(results[:top_k]):
                result['rank'] = i + 1
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"Error searching fallback store: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """Get fallback store statistics"""
        return {
            'total_documents': len(self.documents),
            'dimension': self.embedder.dimension,
            'index_available': True,
            'faiss_available': False
        }
    
    def clear(self):
        """Clear fallback store"""
        self.documents = []
        logger.info("Cleared fallback vector store")


def get_vector_store(index_name: str = "default"):
    """Factory function to get appropriate vector store"""
    if FAISS_AVAILABLE:
        return FAISSVectorStore(index_name)
    else:
        return FallbackVectorStore(index_name)
