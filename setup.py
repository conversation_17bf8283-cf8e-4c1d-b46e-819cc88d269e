"""
Setup script for StudyMate - Document RAG Chatbot
"""
import os
import sys
import subprocess
from pathlib import Path
import shutil


def run_command(command, description=""):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description or command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def check_python_version():
    """Check Python version"""
    print("Checking Python version...")
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("ERROR: Python 3.8 or higher is required")
        return False
    
    print("✅ Python version is compatible")
    return True


def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    
    directories = [
        "data/uploads",
        "data/vector_store", 
        "logs",
        "config",
        "src",
        "tests"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True


def install_dependencies():
    """Install Python dependencies"""
    print("\nInstalling dependencies...")
    
    # Core dependencies that should work on most systems
    core_packages = [
        "streamlit",
        "python-dotenv", 
        "loguru",
        "pandas",
        "numpy",
        "requests",
        "Pillow"
    ]
    
    # Optional dependencies
    optional_packages = [
        "PyMuPDF",
        "faiss-cpu",
        "sentence-transformers",
        "transformers",
        "torch",
        "ibm-watson",
        "pytest"
    ]
    
    # Install core packages
    for package in core_packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}")
    
    # Install optional packages (don't fail if they don't install)
    print("\nInstalling optional packages...")
    for package in optional_packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if success:
            print(f"✅ Installed {package}")
        else:
            print(f"⚠️ Failed to install {package} (optional)")
    
    return True


def setup_environment():
    """Setup environment configuration"""
    print("\nSetting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from .env.example")
        print("⚠️ Please edit .env file with your API keys")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️ No .env.example file found")
    
    return True


def run_tests():
    """Run basic tests"""
    print("\nRunning basic tests...")
    
    # Test imports
    test_imports = [
        "streamlit",
        "pandas", 
        "numpy",
        "requests",
        "PIL",
        "dotenv"
    ]
    
    failed_imports = []
    
    for module in test_imports:
        try:
            __import__(module)
            print(f"✅ {module} import successful")
        except ImportError:
            print(f"❌ {module} import failed")
            failed_imports.append(module)
    
    # Test optional imports
    optional_imports = [
        "fitz",  # PyMuPDF
        "faiss",
        "sentence_transformers",
        "transformers",
        "ibm_watson"
    ]
    
    for module in optional_imports:
        try:
            __import__(module)
            print(f"✅ {module} (optional) import successful")
        except ImportError:
            print(f"⚠️ {module} (optional) import failed")
    
    if failed_imports:
        print(f"\n❌ Some required imports failed: {failed_imports}")
        return False
    
    print("\n✅ All required imports successful")
    return True


def validate_setup():
    """Validate the setup"""
    print("\nValidating setup...")
    
    # Check if main files exist
    required_files = [
        "app.py",
        "config/settings.py",
        "src/document_processor.py",
        "src/embeddings.py",
        "src/vector_store.py",
        "src/chat_engine.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path} exists")
    
    if missing_files:
        print(f"\n❌ Missing required files: {missing_files}")
        return False
    
    print("\n✅ All required files present")
    return True


def main():
    """Main setup function"""
    print("StudyMate - Document RAG Chatbot Setup")
    print("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Creating directories", create_directories),
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Running tests", run_tests),
        ("Validating setup", validate_setup)
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        print(f"\n🔄 {step_name}...")
        try:
            success = step_function()
            if success:
                print(f"✅ {step_name} completed successfully")
            else:
                print(f"❌ {step_name} failed")
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with error: {e}")
            failed_steps.append(step_name)
    
    print("\n" + "=" * 50)
    print("SETUP SUMMARY")
    print("=" * 50)
    
    if failed_steps:
        print(f"❌ Setup completed with issues in: {', '.join(failed_steps)}")
        print("\nPlease review the errors above and fix them manually.")
        print("You may need to install some packages manually or configure API keys.")
    else:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your API keys")
        print("2. Run: streamlit run app.py")
        print("3. Upload documents and start chatting!")
    
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()
