"""
Embeddings service using HuggingFace sentence transformers
"""
import numpy as np
from typing import List, Dict, Any, Optional
from loguru import logger

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("sentence-transformers not available. Using fallback embeddings.")

from config.settings import settings


class HuggingFaceEmbeddingService:
    """Embedding service using HuggingFace sentence transformers"""
    
    def __init__(self):
        self.model_name = settings.HUGGINGFACE_EMBEDDING_MODEL
        self.model = None
        self._load_model()
    
    def _load_model(self):
        """Load the sentence transformer model"""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.error("sentence-transformers not available")
            return
        
        try:
            logger.info(f"Loading embedding model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            self.model = None
    
    def embed_texts(self, texts: List[str]) -> np.ndarray:
        """Generate embeddings for a list of texts"""
        if not self.model:
            raise RuntimeError("Embedding model not available")
        
        if not texts:
            return np.array([])
        
        try:
            # Filter out empty texts
            non_empty_texts = [text for text in texts if text.strip()]
            if not non_empty_texts:
                return np.array([])
            
            logger.info(f"Generating embeddings for {len(non_empty_texts)} texts")
            embeddings = self.model.encode(non_empty_texts, convert_to_numpy=True)
            logger.info(f"Generated embeddings with shape: {embeddings.shape}")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise
    
    def embed_single_text(self, text: str) -> np.ndarray:
        """Generate embedding for a single text"""
        if not text.strip():
            return np.array([])
        
        embeddings = self.embed_texts([text])
        return embeddings[0] if len(embeddings) > 0 else np.array([])
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings"""
        if not self.model:
            return 384  # Default dimension for all-MiniLM-L6-v2
        
        try:
            # Generate a test embedding to get dimension
            test_embedding = self.embed_single_text("test")
            return len(test_embedding)
        except:
            return 384  # Fallback dimension


class FallbackEmbeddingService:
    """Fallback embedding service using simple text features"""
    
    def __init__(self):
        self.dimension = 100  # Fixed dimension for fallback
        logger.warning("Using fallback embedding service")
    
    def embed_texts(self, texts: List[str]) -> np.ndarray:
        """Generate simple embeddings based on text features"""
        if not texts:
            return np.array([])
        
        embeddings = []
        for text in texts:
            embedding = self._simple_text_embedding(text)
            embeddings.append(embedding)
        
        return np.array(embeddings)
    
    def embed_single_text(self, text: str) -> np.ndarray:
        """Generate simple embedding for a single text"""
        return self._simple_text_embedding(text)
    
    def _simple_text_embedding(self, text: str) -> np.ndarray:
        """Create a simple embedding based on text characteristics"""
        if not text.strip():
            return np.zeros(self.dimension)
        
        # Simple features: length, word count, character frequencies, etc.
        features = np.zeros(self.dimension)
        
        # Text length (normalized)
        features[0] = min(len(text) / 1000.0, 1.0)
        
        # Word count (normalized)
        words = text.split()
        features[1] = min(len(words) / 100.0, 1.0)
        
        # Character frequency features
        text_lower = text.lower()
        for i, char in enumerate('abcdefghijklmnopqrstuvwxyz'):
            if i + 2 < self.dimension:
                features[i + 2] = text_lower.count(char) / len(text) if text else 0
        
        # Add some randomness for diversity (but deterministic based on text)
        np.random.seed(hash(text) % 2**32)
        noise = np.random.normal(0, 0.1, self.dimension - 28)
        features[28:] = noise[:self.dimension - 28]
        
        # Normalize
        norm = np.linalg.norm(features)
        if norm > 0:
            features = features / norm
        
        return features
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings"""
        return self.dimension


def get_embedding_service():
    """Factory function to get appropriate embedding service"""
    if SENTENCE_TRANSFORMERS_AVAILABLE:
        return HuggingFaceEmbeddingService()
    else:
        return FallbackEmbeddingService()


class DocumentEmbedder:
    """High-level document embedding service"""
    
    def __init__(self):
        self.embedding_service = get_embedding_service()
        self.dimension = self.embedding_service.get_embedding_dimension()
    
    def embed_document_chunks(self, chunks: List[str], metadata: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Embed document chunks with metadata"""
        if not chunks:
            return {
                'embeddings': np.array([]),
                'chunks': [],
                'metadata': [],
                'dimension': self.dimension
            }
        
        try:
            # Generate embeddings
            embeddings = self.embedding_service.embed_texts(chunks)
            
            # Prepare metadata
            if metadata is None:
                metadata = [{'chunk_index': i} for i in range(len(chunks))]
            elif len(metadata) != len(chunks):
                # Extend or truncate metadata to match chunks
                metadata = metadata[:len(chunks)]
                while len(metadata) < len(chunks):
                    metadata.append({'chunk_index': len(metadata)})
            
            logger.info(f"Embedded {len(chunks)} chunks with dimension {self.dimension}")
            
            return {
                'embeddings': embeddings,
                'chunks': chunks,
                'metadata': metadata,
                'dimension': self.dimension
            }
            
        except Exception as e:
            logger.error(f"Error embedding document chunks: {e}")
            raise
    
    def embed_query(self, query: str) -> np.ndarray:
        """Embed a query for similarity search"""
        try:
            return self.embedding_service.embed_single_text(query)
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            raise
