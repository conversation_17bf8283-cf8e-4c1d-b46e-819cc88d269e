"""
Utility functions for error handling, validation, and common operations
"""
import os
import sys
import traceback
from typing import Any, Dict, List, Optional, Callable
from functools import wraps
from pathlib import Path
import hashlib
import mimetypes
from loguru import logger

from config.settings import settings


class ValidationError(Exception):
    """Custom validation error"""
    pass


class ProcessingError(Exception):
    """Custom processing error"""
    pass


def setup_logging():
    """Setup application logging"""
    try:
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Remove default logger
        logger.remove()
        
        # Add console logger
        logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=settings.LOG_LEVEL
        )
        
        # Add file logger
        logger.add(
            settings.LOG_FILE,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=settings.LOG_LEVEL,
            rotation="1 day",
            retention="7 days",
            compression="zip"
        )
        
        logger.info("Logging setup completed")
        
    except Exception as e:
        print(f"Failed to setup logging: {e}")


def handle_errors(func: Callable) -> Callable:
    """Decorator for error handling"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {func.__name__}: {e}")
            return {
                'success': False,
                'error': str(e),
                'error_type': 'validation'
            }
        except ProcessingError as e:
            logger.error(f"Processing error in {func.__name__}: {e}")
            return {
                'success': False,
                'error': str(e),
                'error_type': 'processing'
            }
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                'success': False,
                'error': f"Unexpected error: {str(e)}",
                'error_type': 'unexpected'
            }
    return wrapper


def validate_file_upload(file_content: bytes, filename: str) -> Dict[str, Any]:
    """Validate uploaded file"""
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'file_info': {}
    }
    
    try:
        # Basic validations
        if not file_content:
            validation_result['valid'] = False
            validation_result['errors'].append("File is empty")
            return validation_result
        
        if not filename:
            validation_result['valid'] = False
            validation_result['errors'].append("Filename is missing")
            return validation_result
        
        # File size validation
        file_size = len(file_content)
        max_size = settings.MAX_FILE_SIZE_MB * 1024 * 1024
        
        if file_size > max_size:
            validation_result['valid'] = False
            validation_result['errors'].append(
                f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({settings.MAX_FILE_SIZE_MB}MB)"
            )
        
        # File extension validation
        file_path = Path(filename)
        file_ext = file_path.suffix.lower()
        
        supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'}
        if file_ext not in supported_extensions:
            validation_result['valid'] = False
            validation_result['errors'].append(
                f"Unsupported file extension: {file_ext}. Supported: {', '.join(supported_extensions)}"
            )
        
        # MIME type validation
        mime_type, _ = mimetypes.guess_type(filename)
        if mime_type:
            if not (mime_type.startswith('image/') or mime_type == 'application/pdf'):
                validation_result['warnings'].append(f"Unexpected MIME type: {mime_type}")
        
        # File info
        validation_result['file_info'] = {
            'filename': filename,
            'size_bytes': file_size,
            'size_mb': round(file_size / 1024 / 1024, 2),
            'extension': file_ext,
            'mime_type': mime_type,
            'hash': hashlib.md5(file_content).hexdigest()[:8]
        }
        
        logger.info(f"File validation completed for {filename}: {'valid' if validation_result['valid'] else 'invalid'}")
        
    except Exception as e:
        validation_result['valid'] = False
        validation_result['errors'].append(f"Validation error: {str(e)}")
        logger.error(f"Error validating file {filename}: {e}")
    
    return validation_result


def validate_text_input(text: str, min_length: int = 1, max_length: int = 10000) -> Dict[str, Any]:
    """Validate text input"""
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'cleaned_text': text
    }
    
    try:
        if not isinstance(text, str):
            validation_result['valid'] = False
            validation_result['errors'].append("Input must be a string")
            return validation_result
        
        # Length validation
        text_length = len(text.strip())
        
        if text_length < min_length:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Text too short (minimum {min_length} characters)")
        
        if text_length > max_length:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Text too long (maximum {max_length} characters)")
        
        # Clean text
        cleaned_text = text.strip()
        if cleaned_text != text:
            validation_result['warnings'].append("Text was trimmed")
            validation_result['cleaned_text'] = cleaned_text
        
        # Check for potentially problematic content
        if len(cleaned_text.split()) < 1:
            validation_result['warnings'].append("Text contains no words")
        
    except Exception as e:
        validation_result['valid'] = False
        validation_result['errors'].append(f"Text validation error: {str(e)}")
        logger.error(f"Error validating text input: {e}")
    
    return validation_result


def safe_file_operation(operation: Callable, *args, **kwargs) -> Dict[str, Any]:
    """Safely perform file operations"""
    try:
        result = operation(*args, **kwargs)
        return {
            'success': True,
            'result': result
        }
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
        return {
            'success': False,
            'error': f"File not found: {str(e)}",
            'error_type': 'file_not_found'
        }
    except PermissionError as e:
        logger.error(f"Permission denied: {e}")
        return {
            'success': False,
            'error': f"Permission denied: {str(e)}",
            'error_type': 'permission_denied'
        }
    except OSError as e:
        logger.error(f"OS error: {e}")
        return {
            'success': False,
            'error': f"System error: {str(e)}",
            'error_type': 'os_error'
        }
    except Exception as e:
        logger.error(f"Unexpected file operation error: {e}")
        return {
            'success': False,
            'error': f"Unexpected error: {str(e)}",
            'error_type': 'unexpected'
        }


def validate_environment() -> Dict[str, Any]:
    """Validate environment setup"""
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'services': {}
    }
    
    try:
        # Check required directories
        required_dirs = [
            settings.UPLOAD_DIR,
            settings.VECTOR_STORE_DIR,
            "logs"
        ]
        
        for dir_path in required_dirs:
            path = Path(dir_path)
            if not path.exists():
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    validation_result['warnings'].append(f"Created missing directory: {dir_path}")
                except Exception as e:
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Cannot create directory {dir_path}: {e}")
        
        # Check API keys and services
        services_status = {
            'mistral': bool(settings.MISTRAL_API_KEY),
            'ibm_watson': bool(settings.IBM_WATSON_API_KEY and settings.IBM_WATSON_ASSISTANT_ID),
            'huggingface': bool(settings.HUGGINGFACE_API_KEY)
        }
        
        validation_result['services'] = services_status
        
        # Check if at least one LLM service is available
        if not any([services_status['mistral'], services_status['ibm_watson']]):
            validation_result['warnings'].append("No LLM service configured (Mistral or IBM Watson)")
        
        # Check Python packages
        try:
            import streamlit
            validation_result['services']['streamlit'] = True
        except ImportError:
            validation_result['valid'] = False
            validation_result['errors'].append("Streamlit not available")
        
        try:
            import faiss
            validation_result['services']['faiss'] = True
        except ImportError:
            validation_result['warnings'].append("FAISS not available, using fallback vector store")
            validation_result['services']['faiss'] = False
        
        try:
            from sentence_transformers import SentenceTransformer
            validation_result['services']['sentence_transformers'] = True
        except ImportError:
            validation_result['warnings'].append("sentence-transformers not available, using fallback embeddings")
            validation_result['services']['sentence_transformers'] = False
        
        logger.info(f"Environment validation completed: {'valid' if validation_result['valid'] else 'invalid'}")
        
    except Exception as e:
        validation_result['valid'] = False
        validation_result['errors'].append(f"Environment validation error: {str(e)}")
        logger.error(f"Error validating environment: {e}")
    
    return validation_result


def format_error_message(error: Any, context: str = "") -> str:
    """Format error message for user display"""
    try:
        if isinstance(error, dict):
            if 'error' in error:
                return f"{context}: {error['error']}" if context else error['error']
            elif 'errors' in error:
                errors = error['errors']
                if isinstance(errors, list):
                    return f"{context}: {'; '.join(errors)}" if context else '; '.join(errors)
                else:
                    return f"{context}: {errors}" if context else str(errors)
        
        error_str = str(error)
        return f"{context}: {error_str}" if context else error_str
        
    except Exception:
        return f"{context}: Unknown error occurred" if context else "Unknown error occurred"


def get_system_info() -> Dict[str, Any]:
    """Get system information for debugging"""
    try:
        import platform
        import psutil
        
        return {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_count': os.cpu_count(),
            'memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_free_gb': round(psutil.disk_usage('.').free / (1024**3), 2),
            'working_directory': os.getcwd()
        }
    except Exception as e:
        logger.warning(f"Could not get system info: {e}")
        return {'error': str(e)}
